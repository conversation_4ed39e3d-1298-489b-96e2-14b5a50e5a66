import httpx
import json
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger("webook_pro")

class WebookClient:
    """
    A client for interacting with the Webook API, designed to mimic a real browser session
    to avoid Cloudflare's 403 errors.
    """
    def __init__(self, proxy: Optional[str] = None):
        self.base_url = "https://api.webook.com/api/v2"
        self.proxy = proxy
        self.headers = {
            'accept': 'application/json',
            'origin': 'https://webook.com',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'token': 'e9aac1f2f0b6c07d6be070ed14829de684264278359148d6a582ca65a50934d2',
        }
        self.client = self._create_client()

    def _create_client(self) -> httpx.Client:
        """Creates and configures an httpx client."""
        if self.proxy and "://" not in self.proxy:
            parts = self.proxy.split(":")
            if len(parts) == 4:
                proxy_host, proxy_port, proxy_user, proxy_pass = parts
                self.proxy = f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}"
        return httpx.Client(
            proxy=self.proxy,
            headers=self.headers,
            timeout=10.0,
            verify=False
        )

    def get_event_info(self, event_key: str, is_season: bool = False) -> Optional[Dict[str, Any]]:
        """
        Retrieves event details from the Webook API.
        """
        path = f'season-detail/{event_key}' if is_season else f'event-detail/{event_key}'
        params = {'lang': 'en', 'visible_in': 'rs'}
        
        try:
            response = self.client.get(f"{self.base_url}/{path}", params=params)
            response.raise_for_status()  # Raise an exception for bad status codes
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching event info for {event_key}: {e.response.status_code} - {e.response.text}")
            return None
        except httpx.RequestError as e:
            logger.error(f"Request error fetching event info for {event_key}: {e}")
            return None

    def get_hold_token(self, event_id: str, access_token: str) -> Optional[str]:
        """
        Retrieves a hold token for a given event.
        """
        url = f"{self.base_url}/seats/hold-token"
        headers = self.headers.copy()
        headers['authorization'] = f'Bearer {access_token}'
        payload = {'event_id': event_id, 'lang': 'en'}

        try:
            response = self.client.post(url, json=payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            return data.get('data', {}).get('token')
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching hold token: {e.response.status_code} - {e.response.text}")
            if e.response.status_code in [401, 403]:
                logger.warning(f"Authentication failed for token {access_token[:8]}... Reporting as invalid.")
                # You may want to add logic here to report the invalid token
            return None
        except httpx.RequestError as e:
            logger.error(f"Request error fetching hold token: {e}")
            return None

    def close(self):
        """Closes the HTTP client."""
        self.client.close()