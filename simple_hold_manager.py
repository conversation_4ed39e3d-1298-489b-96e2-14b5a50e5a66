"""
Simple Hold Manager - A clean replacement for the complex FastHoldManager
Uses existing async functions from helper.py for seat operations
"""

import asyncio
import logging
import time
from typing import Optional, List, Dict, Any
from PyQt5.QtCore import QObject, pyqtSignal

from helper import hold_seat, release_seat, switch_seat_immediate
from token_retrieval import get_hold_token, get_cached_event_id

logger = logging.getLogger("webook_pro")


class SimpleHoldManager(QObject):
    """
    A simplified seat holding manager that uses the existing async functions
    from helper.py instead of reimplementing everything.
    """
    
    # Signals
    seat_held_signal = pyqtSignal(str, str)  # seat_id, token_id
    seat_released_signal = pyqtSignal(str)   # seat_id
    log_signal = pyqtSignal(str)
    performance_signal = pyqtSignal(dict)
    
    def __init__(self, event_key: str, chart_key: str, channel_keys: List[str], 
                 team_id: str, proxy: Optional[str] = None, event_id: Optional[str] = None):
        super().__init__()
        
        self.event_key = event_key
        self.chart_key = chart_key
        self.channel_keys = channel_keys or ['NO_CHANNEL']
        self.team_id = team_id
        self.proxy = proxy
        self.event_id = event_id
        
        # Current hold token
        self.current_token = None
        self.token_expire_time = None
        
        # Performance tracking
        self.performance_stats = {
            'holds_attempted': 0,
            'holds_successful': 0,
            'average_response_time': 0.0,
            'last_response_times': []
        }
        
        # Initialize with a token
        self._initialize_token()
    
    def _initialize_token(self):
        """Get an initial hold token"""
        try:
            # Run async token retrieval
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                event_id = self.event_id or get_cached_event_id()
                if event_id:
                    self.current_token = loop.run_until_complete(
                        get_hold_token(event_id=event_id, proxy=self.proxy)
                    )
                    if self.current_token:
                        self.token_expire_time = time.time() + (10 * 60)  # 10 minutes
                        self.log_signal.emit(f"✅ Initialized with hold token: {self.current_token[:8]}...")
                    else:
                        self.log_signal.emit("⚠️ Failed to get initial hold token")
                else:
                    self.log_signal.emit("⚠️ No event ID available for token creation")
            finally:
                loop.close()
        except Exception as e:
            self.log_signal.emit(f"❌ Error initializing token: {str(e)}")
            logger.error(f"Error initializing token: {str(e)}")
    
    def _ensure_valid_token(self) -> bool:
        """Ensure we have a valid, non-expired token"""
        if not self.current_token or (self.token_expire_time and time.time() > self.token_expire_time):
            self.log_signal.emit("🔄 Token expired, getting new token...")
            self._initialize_token()
        
        return self.current_token is not None
    
    def hold_seat_async(self, seat_id: str) -> bool:
        """
        Hold a seat using the async helper function
        Returns True if successful, False otherwise
        """
        if not self._ensure_valid_token():
            self.log_signal.emit(f"❌ No valid token available to hold seat {seat_id}")
            return False
        
        try:
            start_time = time.time()
            
            # Run the async hold_seat function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                success = loop.run_until_complete(
                    hold_seat(
                        seat_number=seat_id,
                        event_key=self.event_key,
                        hold_token=self.current_token,
                        channel_keys=self.channel_keys,
                        proxy=self.proxy
                    )
                )
                
                # Track performance
                response_time = (time.time() - start_time) * 1000  # Convert to ms
                self._update_performance_stats(success, response_time)
                
                if success:
                    self.seat_held_signal.emit(seat_id, self.current_token)
                    self.log_signal.emit(f"✅ Successfully held seat {seat_id} in {response_time:.1f}ms")
                else:
                    self.log_signal.emit(f"❌ Failed to hold seat {seat_id}")
                
                return success
                
            finally:
                loop.close()
                
        except Exception as e:
            self.log_signal.emit(f"❌ Error holding seat {seat_id}: {str(e)}")
            logger.error(f"Error holding seat {seat_id}: {str(e)}")
            return False
    
    def release_seat_async(self, seat_id: str, token: Optional[str] = None) -> bool:
        """
        Release a seat using the async helper function
        """
        release_token = token or self.current_token
        if not release_token:
            self.log_signal.emit(f"❌ No token available to release seat {seat_id}")
            return False
        
        try:
            start_time = time.time()
            
            # Run the async release_seat function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                success = loop.run_until_complete(
                    release_seat(
                        seat_number=seat_id,
                        event_key=self.event_key,
                        hold_token=release_token,
                        proxy=self.proxy
                    )
                )
                
                response_time = (time.time() - start_time) * 1000
                
                if success:
                    self.seat_released_signal.emit(seat_id)
                    self.log_signal.emit(f"✅ Successfully released seat {seat_id} in {response_time:.1f}ms")
                else:
                    self.log_signal.emit(f"❌ Failed to release seat {seat_id}")
                
                return success
                
            finally:
                loop.close()
                
        except Exception as e:
            self.log_signal.emit(f"❌ Error releasing seat {seat_id}: {str(e)}")
            logger.error(f"Error releasing seat {seat_id}: {str(e)}")
            return False
    
    def switch_seat_async(self, seat_id: str, old_token: str, new_token: str) -> bool:
        """
        Switch a seat from one token to another using the async helper function
        """
        try:
            start_time = time.time()
            
            # Run the async switch_seat_immediate function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                success = loop.run_until_complete(
                    switch_seat_immediate(
                        seat_number=seat_id,
                        event_key=self.event_key,
                        old_token=old_token,
                        new_token=new_token,
                        channel_keys=self.channel_keys,
                        proxy=self.proxy
                    )
                )
                
                response_time = (time.time() - start_time) * 1000
                
                if success:
                    self.log_signal.emit(f"✅ Successfully switched seat {seat_id} in {response_time:.1f}ms")
                else:
                    self.log_signal.emit(f"❌ Failed to switch seat {seat_id}")
                
                return success
                
            finally:
                loop.close()
                
        except Exception as e:
            self.log_signal.emit(f"❌ Error switching seat {seat_id}: {str(e)}")
            logger.error(f"Error switching seat {seat_id}: {str(e)}")
            return False
    
    def _update_performance_stats(self, success: bool, response_time_ms: float):
        """Update performance statistics"""
        self.performance_stats['holds_attempted'] += 1
        
        if success:
            self.performance_stats['holds_successful'] += 1
        
        # Track last 50 response times for average calculation
        self.performance_stats['last_response_times'].append(response_time_ms)
        if len(self.performance_stats['last_response_times']) > 50:
            self.performance_stats['last_response_times'].pop(0)
        
        # Calculate average
        if self.performance_stats['last_response_times']:
            self.performance_stats['average_response_time'] = sum(
                self.performance_stats['last_response_times']
            ) / len(self.performance_stats['last_response_times'])
        
        # Emit performance signal
        self.performance_signal.emit(self.performance_stats.copy())
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        return self.performance_stats.copy()
    
    def renew_token_if_needed(self):
        """Check if token needs renewal and renew if necessary"""
        if not self.current_token or (self.token_expire_time and time.time() > (self.token_expire_time - 60)):
            self.log_signal.emit("🔄 Renewing token...")
            self._initialize_token()
    
    def cleanup(self):
        """Clean up resources"""
        self.current_token = None
        self.token_expire_time = None
        self.log_signal.emit("🧹 SimpleHoldManager cleaned up")
