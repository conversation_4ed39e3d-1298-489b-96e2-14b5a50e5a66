# main_window.py - Cleaned up and optimized version
import re
import time
import logging
from collections import defaultdict
import queue
from threading import Lock, Thread
import traceback
from PyQt5.QtWidgets import (
    QMainWindow, QApplication, QMessageBox, QTabWidget, QLineEdit,
    QTextEdit, QPushButton, QVBoxLayout, QW<PERSON>t, QHBoxLayout, QFormLayout,
    QLabel, QComboBox, QCheckBox
)
from PyQt5.QtCore import Qt, QEvent, QThread, QCoreApplication, QTimer, QThreadPool
import random
import threading
from PyQt5.QtCore import QTime, QTimer
import json
import asyncio
# Your existing helpers
from helper import (
    hold_seat,
    get_object_statuses,
    group_tickets_by_type_and_status,
    release_seat,
    get_event_seatsio_info
)
from elitesoftworks import print_logo, get_machine_hwid
from proxy_config_dialog import ProxyConfigDialog
from token_retrieval import cache_event_id, get_cached_event_id
from webook_client import WebookClient

# Manager, threads, tabs, workers
from chart_token_manager import get_chart_token
from manager.proxy_manager import get_global_proxy_manager
from simple_hold_manager import SimpleHoldManager
from threads.time_left_updater_thread import TimeLeftUpdaterThread
from threads.event_websocket_thread import WebSocketManager
from token_management_system import TokenManagementSystem
from tabs.held_seats_tab import HeldSeatsTab
from tabs.ticket_type_selection_tab import TicketTypeSelectionTab
from performance_testing import PerformanceStats, PerformanceMonitorDialog
from script_info import SCRIPT_NAME, VERSION, MAX_SEATS_PER_TOKEN

logger = logging.getLogger("webook_pro")

def perform_hwid_check(username: str):
    """
    Stub function. Replace with real logic if needed.
    """
    return True


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(f"{SCRIPT_NAME} v{VERSION}")
        self.resize(1000, 600)

        # Initialize data structures
        self.tickets_info = defaultdict(lambda: defaultdict(dict))  # type -> status -> {seat_id: seat_data}
        self.seat_id_map = {}  # seat_id: {'type': str, 'status': str}

        # Setup UI components
        self.tab_widget = QTabWidget()
        self.setCentralWidget(self.tab_widget)

        self.booking_tab = QWidget()
        self.create_booking_tab_ui()
        self.tab_widget.addTab(self.booking_tab, "Booking")

        self.held_seats_tab = HeldSeatsTab(self)
        self.held_seats_tab.setEnabled(False)
        self.tab_widget.addTab(self.held_seats_tab, "Held Seats")

        self.ticket_type_tab = TicketTypeSelectionTab(self)
        self.ticket_type_tab.setEnabled(False)
        self.tab_widget.addTab(self.ticket_type_tab, "Ticket Type Selection")

        # Initialize proxy config
        self.proxy_config = {
            "enabled": False,
            "domain": "p.webshare.io",
            "port": "80",
            "username": "taplmftg-rotate",
            "password": "P@SSWORD"
        }

        # Initialize chart token refresher
        self.initialize_chart_token()

        # Core system components
        self.token_system = None
        self.simple_hold_manager = None
        self.websocket_manager = None

        # Threading and concurrency management
        self.booking_lock = Lock()
        self.data_lock = Lock()
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(4)

        # Initialize updater thread
        self.time_left_updater = TimeLeftUpdaterThread(interval=1)
        self.time_left_updater.setObjectName("TimeLeftUpdaterThread")
        self.time_left_updater.update_signal.connect(self.held_seats_tab.auto_refresh_held_seats)
        self.time_left_updater.refresh_signal.connect(self.refresh_all_token_times)
        self.time_left_updater.start()

        # Auto-hold tracking
        self.pending_seats = 0
        self.auto_held_seats = {}

        # Start cleanup timer
        self.cleanup_timer = QTimer()
        self.cleanup_timer.timeout.connect(self.cleanup_zombie_seats)
        self.cleanup_timer.start(10000)  # Run cleanup every 10 seconds

    #------------------------------------------------------------------
    # Core initialization and cleanup methods
    #------------------------------------------------------------------

    def initialize_chart_token(self):
        """Initialize chart token when the application starts"""
        # Force an initial fetch of the chart token
        chart_token = get_chart_token(force_refresh=True, proxy=self.get_proxy_string())
        if chart_token:
            self.log(f"🔑 Chart token initialized: {chart_token[:8]}...")
        else:
            self.log("⚠️ Failed to initialize chart token")

    def reset_auto_held_seats(self):
        """
        Reset the auto_held_seats dictionary and update the window title.
        This allows the system to try holding seats that previously failed.
        """
        with self.data_lock:
            self.auto_held_seats = {}
            self.pending_seats = 0

        # Reset window title
        current_title = self.windowTitle()
        if '(' in current_title:
            base_title = current_title.split('(')[0].strip()
            held_count = 0
            if self.token_system:
                held_count = len(self.token_system.get_all_seats())

            if held_count > 0:
                self.setWindowTitle(f"{base_title} (Auto-held: {held_count})")
            else:
                self.setWindowTitle(base_title)

        self.log("🔄 Auto-hold tracking reset - previously failed seats will be eligible for auto-hold")

        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

    def _initialize_token_management_system(self):
        """Initialize the centralized token management system"""
        # Create the token management system
        self.token_system = TokenManagementSystem(
            event_key=self.webook_data['data']['seats_io']['event_key'],
            chart_key=self.webook_data['data']['seats_io']['chart_key'],
            channel_keys=self.webook_data['data']['channel_keys'],
            team_id=self.team_combo.currentData(),
            proxies=[self.get_proxy_string()] if self.get_proxy_string() else []
        )

        self.token_system.log_signal.connect(self.log)
        self.token_system.seat_held_signal.connect(self.on_seat_held)
        self.token_system.seat_released_signal.connect(self.on_seat_released)
        self.token_system.seat_transferred_signal.connect(self.on_seat_transferred)
        self.token_system.seat_expired_signal.connect(self.on_seats_expired)
        self.token_system.ui_update_signal.connect(self.held_seats_tab.auto_refresh_held_seats)
        self.token_system.token_renewed_signal.connect(self.on_token_renewed)

        self.log("TokenManagementSystem initialized")


    def initialize_proxy_system(self):
        """Initialize the proxy system with current settings"""
        # Initialize the global proxy manager with current settings
        proxy_manager = get_global_proxy_manager(self.proxy_config)

        # Log proxy configuration
        if self.proxy_config.get("enabled", False):
            if self.proxy_config.get("mode", "single") == "single":
                domain = self.proxy_config.get("domain", "")
                port = self.proxy_config.get("port", "")
                user = self.proxy_config.get("username", "")
                rotating = self.proxy_config.get("use_rotating", True)

                if rotating:
                    self.log(f"Proxy enabled: {domain}:{port} with rotation (User: {user})")
                else:
                    self.log(f"Proxy enabled: {domain}:{port} without rotation (User: {user})")
            else:
                proxy_count = len(self.proxy_config.get("proxy_list", []))
                rotation = self.proxy_config.get("local_rotation", True)
                rotation_count = self.proxy_config.get("rotation_count", 50)

                if rotation:
                    self.log(f"Local proxy rotation enabled with {proxy_count} proxies. Rotating every {rotation_count} requests.")
                else:
                    self.log(f"Local proxy list enabled with {proxy_count} proxies. No automatic rotation.")


    def _start_simple_holding_system(self):
        """Initialize the SimpleHoldManager for seat holding"""
        # Get event_id if available
        event_id = None
        if hasattr(self, 'webook_data') and self.webook_data:
            event_id = self.webook_data.get("data", {}).get("_id")

        self.simple_hold_manager = SimpleHoldManager(
            event_key=self.webook_data['data']['seats_io']['event_key'],
            chart_key=self.webook_data['data']['seats_io']['chart_key'],
            channel_keys=self.webook_data['data']['channel_keys'],
            team_id=self.team_combo.currentData(),
            proxy=self.get_proxy_string(),
            event_id=event_id
        )

        # Connect signals
        self.simple_hold_manager.seat_held_signal.connect(self.on_simple_hold_seat_held)
        self.simple_hold_manager.seat_released_signal.connect(self.on_simple_hold_seat_released)
        self.simple_hold_manager.log_signal.connect(self.log)
        self.simple_hold_manager.performance_signal.connect(self.on_performance_signal)

        self._start_token_renewal_timer()



    def _start_token_renewal_timer(self):
        """Start a timer to periodically check if tokens need renewal"""
        self.renewal_timer = QTimer()
        self.renewal_timer.timeout.connect(self._check_and_renew_token)
        self.renewal_timer.start(60 * 1000) # Check every minute

    def _check_and_renew_token(self):
        """Check if the SimpleHoldManager token needs renewal"""
        if hasattr(self, 'simple_hold_manager') and self.simple_hold_manager:
            self.simple_hold_manager.renew_token_if_needed()

    def _initialize_websocket(self, data):
        """Initialize WebSocket connection for real-time seat status updates"""
        if self.websocket_manager and self.websocket_manager.thread.isRunning():
            self.websocket_manager.stop()

        # Handle season_event_key correctly
        season_event_key = data['event_info'].get('season_info', {})
        if season_event_key:
            season_event_key = season_event_key.get('topLevelSeasonKey')
        event_key = season_event_key or data["event_key"]

        self.websocket_manager = WebSocketManager(
            event_key,
            data["chart_key"],
            parent=None
        )
        self.websocket_manager.seat_data_updated.connect(self.process_single_seat_update)
        self.websocket_manager.error_signal.connect(self.on_refresh_error)
        self.websocket_manager.connected_signal.connect(self.on_websocket_connected)
        self.websocket_manager.start()

    def _update_initial_ui(self):
        """Initial UI update after loading event data"""
        self.ticket_type_tab.update_tickets_info(self.tickets_info)
        self.held_seats_tab.auto_refresh_held_seats()

    #------------------------------------------------------------------
    # Token and seat event handlers
    #------------------------------------------------------------------

    def on_async_seat_held(self, seat_number, token):
        """
        Handler for seats held by the async hold system.
        """
        try:
            with self.data_lock:
                # Reduce pending count immediately
                self.pending_seats = max(0, self.pending_seats - 1)

                # Remove from auto_held_seats to allow holding other seats
                self.auto_held_seats.pop(seat_number, None)

            self.log(f"✅ Held seat {seat_number}")

            # Check if max tickets reached
            max_tickets = 0
            try:
                max_tickets_text = self.total_tickets_edit.text()
                if max_tickets_text:
                    max_tickets = int(max_tickets_text)
            except ValueError:
                pass

            # Only check max tickets if there's a limit
            if max_tickets > 0:
                # Get seat count from token system directly
                if self.token_system:
                    held_seats = len(self.token_system.get_all_seats())
                    if held_seats > max_tickets:
                        # Release excess seat in background
                        self._release_excess_seat(seat_number, token)
                        return

            # Register with TokenManagementSystem
            if self.token_system:
                self.token_system.register_held_seat(seat_number, token)

                # Trigger UI update asynchronously
                QTimer.singleShot(50, lambda: self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True))
                QTimer.singleShot(75, self._update_window_title)

        except Exception as e:
            # Silent exception handling on critical path
            pass


    def initialize_channel_key_refresh_timer(self):
        """Initialize timer for checking channel key updates"""
        self.channel_key_timer = QTimer()
        self.channel_key_timer.timeout.connect(self.check_channel_key_updates)
        
        # Calculate time until next half-hour mark
        current_time = QTime.currentTime()
        current_minute = current_time.minute()
        current_second = current_time.second()
        
        # Calculate milliseconds until next check time (start of hour or half-hour)
        if current_minute < 30:
            # Time until 30-minute mark
            minutes_to_wait = 30 - current_minute
        else:
            # Time until next hour
            minutes_to_wait = 60 - current_minute
        
        # Subtract seconds already elapsed in the current minute
        seconds_to_wait = minutes_to_wait * 60 - current_second
        msecs_to_wait = seconds_to_wait * 1000
        
        # Start one-shot timer for first alignment to hour/half-hour
        QTimer.singleShot(msecs_to_wait, self.start_periodic_channel_key_check)
        
        self.log("🔄 Channel key refresh timer initialized. First check in " + 
                f"{minutes_to_wait} minutes and {current_second} seconds")

    def start_periodic_channel_key_check(self):
        """Start the periodic channel key check timer"""
        # Now we're aligned to hour/half-hour, start the regular 30-minute timer
        self.channel_key_timer.start(30 * 60 * 1000)  # 30 minutes in milliseconds
        
        # Also perform the first check immediately
        self.check_channel_key_updates()

    def check_channel_key_updates(self):
        """Check for updates to channel keys and reload if necessary"""
        if not hasattr(self, 'webook_data') or not self.webook_data:
            self.log("⚠️ No event loaded, skipping channel key check")
            return
        
        try:
            # Get current time for logging
            current_time = QTime.currentTime()
            time_str = current_time.toString("HH:mm:ss")
            self.log(f"🔍 {time_str} - Scheduled channel key check running...")
            
            # Use the enhanced reload_event_data method
            event_key = self.webook_data['data']['seats_io']['event_key']
            success = self.reload_event_data(event_key)
            
            if success:
                self.log(f"✓ {time_str} - Channel key check completed successfully")
                
                # Update the window title to reflect any changes in held seats
                self._update_window_title()
                
                # Force refresh of held seats tab to ensure accurate data
                if hasattr(self, 'held_seats_tab'):
                    self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
                
                # Update ticket type selection tab to ensure accurate data
                if hasattr(self, 'ticket_type_tab'):
                    self.ticket_type_tab.update_tickets_info(self.tickets_info)
            else:
                self.log(f"⚠️ {time_str} - Channel key check failed, will retry at next scheduled time")
                
        except Exception as e:
            self.log(f"❌ Error in scheduled channel key check: {str(e)}")
            logger.error(f"Error in scheduled channel key check: {str(e)}", exc_info=True)



    def on_seats_removed(self, expired_seats):
        """Handle seats that have been removed due to expiration"""
        if not expired_seats:
            return

        # Log the removals
        self.log(f"⚠️ {len(expired_seats)} seats removed due to token expiration")

        # Update the seat data structures
        with self.data_lock:
            for seat_id in expired_seats:
                # Remove from auto_held_seats if it's there
                self.auto_held_seats.pop(seat_id, None)

                # Update seat status in tickets_info if we know about it
                if seat_id in self.seat_id_map:
                    seat_type = self.seat_id_map[seat_id]['type']
                    old_status = self.seat_id_map[seat_id]['status']

                    # Remove from old status dictionary
                    if seat_type in self.tickets_info and old_status in self.tickets_info[seat_type]:
                        self.tickets_info[seat_type][old_status].pop(seat_id, None)

                    # Update seat status to unknown (it might become free again)
                    self.seat_id_map[seat_id]['status'] = 'unknown'

        # Update the UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

    def on_seat_held(self, seat_id, token_id):
        """Handle new seat successfully held by TokenManagementSystem"""
        with self.data_lock:
            self.auto_held_seats.pop(seat_id, None)
            self.pending_seats -= 1

        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

        # Update window title
        self._update_window_title()

    def on_seat_released(self, seat_id):
        """Handle seat released by TokenManagementSystem"""
        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

        # Update window title
        self._update_window_title()

    def on_seat_transferred(self, seat_id, token_id, success):
        """Handle seat transfer result from TokenManagementSystem"""
        if success:
            self.log(f"Seat {seat_id} transferred to token {token_id}")
            with self.data_lock:
                if seat_id in self.seat_id_map:
                    seat_type = self.seat_id_map[seat_id]['type']
                    old_status = self.seat_id_map[seat_id]['status']
                    if seat_type in self.tickets_info and old_status in self.tickets_info[seat_type]:
                        self.tickets_info[seat_type][old_status].pop(seat_id, None)
                    self.seat_id_map[seat_id]['status'] = 'transferred'
        else:
            self.log(f"Failed to transfer seat {seat_id} to token {token_id}")
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
        self._update_window_title()

    def on_seats_expired(self, seat_ids):
        """Handle seats expired in TokenManagementSystem"""
        self.log(f"⚠️ {len(seat_ids)} seats expired")

        # Update seat status in tickets_info
        with self.data_lock:
            for seat_id in seat_ids:
                # Update seat status in tickets_info if we know about it
                if seat_id in self.seat_id_map:
                    seat_type = self.seat_id_map[seat_id]['type']
                    old_status = self.seat_id_map[seat_id]['status']

                    # Remove from old status dictionary
                    if seat_type in self.tickets_info and old_status in self.tickets_info[seat_type]:
                        self.tickets_info[seat_type][old_status].pop(seat_id, None)

                    # Update seat status to unknown
                    self.seat_id_map[seat_id]['status'] = 'unknown'

        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

        # Update window title
        self._update_window_title()

    def on_simple_hold_seat_held(self, seat_id: str, token_id: str):
        """Handle seat held by SimpleHoldManager"""
        with self.data_lock:
            # Remove from auto_held_seats to allow holding other seats
            self.auto_held_seats.pop(seat_id, None)
            self.pending_seats = max(0, self.pending_seats - 1)

        # Register with TokenManagementSystem if available
        if self.token_system:
            self.token_system.register_held_seat(seat_id, token_id)

        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
        self._update_window_title()

    def on_simple_hold_seat_released(self, seat_id: str):
        """Handle seat released by SimpleHoldManager"""
        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
        self._update_window_title()

    def on_performance_signal(self, stats: dict):
        """Handle performance statistics from SimpleHoldManager"""
        # Update performance stats if we have a performance monitor
        if hasattr(self, 'performance_stats'):
            # Update our performance stats with the new data
            if 'average_response_time' in stats:
                self.performance_stats.record_network_time(stats['average_response_time'])

        # Log performance info occasionally
        if stats.get('holds_attempted', 0) % 10 == 0 and stats.get('holds_attempted', 0) > 0:
            success_rate = (stats.get('holds_successful', 0) / stats.get('holds_attempted', 1)) * 100
            avg_time = stats.get('average_response_time', 0)
            self.log(f"📊 Hold performance: {success_rate:.1f}% success rate, {avg_time:.1f}ms avg response time")

    def _update_window_title(self):
        """Update window title with current held seat count"""
        try:
            # Only use token_system for seat count
            held_seats_count = 0
            if self.token_system:
                held_seats_count = len(self.token_system.get_all_seats())
            else:
                return  # Don't update if no token system

            current_title = self.windowTitle()
            if '(' in current_title and 'Auto-held:' in current_title:
                base_title = current_title.split('(')[0].strip()
                self.setWindowTitle(f"{base_title} (Auto-held: {held_seats_count})")
        except Exception as e:
            logger.error(f"Error updating window title: {str(e)}")


    def _release_excess_seat(self, seat_number, token):
        """Release excess seat using async method"""
        self.release_seat_sync(seat_number, token)
        self.log(f"Releasing excess seat: {seat_number}")
    #------------------------------------------------------------------
    # Seat data processing and auto-hold logic
    #------------------------------------------------------------------

    def cleanup_zombie_seats(self):
        """
        Automatically clean up "zombie" seats - those that are in auto_held_seats
        but aren't actually being processed or held successfully.
        Updated to use only token_system.
        """
        with self.data_lock:
            # Nothing to clean if no auto-held seats
            if not self.auto_held_seats:
                return

            # Seats that are actually held by token system
            actually_held_seats = set()
            if self.token_system:
                actually_held_seats = set(self.token_system.get_all_seats().keys())
            else:
                # No token system
                return

            # Find zombie seats - in auto_held_seats but not in token system
            zombie_seats = []

            for seat_id in list(self.auto_held_seats.keys()):
                if seat_id not in actually_held_seats:
                    zombie_seats.append(seat_id)
                    self.auto_held_seats.pop(seat_id, None)

            # Adjust pending_seats if needed to match reality
            total_held = len(actually_held_seats)
            total_auto_held = len(self.auto_held_seats)

            # If pending_seats is out of sync, reset it based on auto_held_seats
            if self.pending_seats != total_auto_held:
                self.log(f"⚠️ Auto-hold count mismatch: pending={self.pending_seats}, tracked={total_auto_held}")
                self.pending_seats = total_auto_held

            # If we found zombie seats, log and update
            if zombie_seats:
                self.log(f"🧟 Cleaned up {len(zombie_seats)} zombie seats from auto-hold tracking")

                # Update window title to show correct count
                current_title = self.windowTitle()
                if '(' in current_title and 'Auto-held:' in current_title:
                    base_title = current_title.split('(')[0].strip()
                    self.setWindowTitle(f"{base_title} (Auto-held: {total_held})")

                # Update ticket type UI to reflect freed seats
                for seat_id in zombie_seats:
                    if seat_id in self.seat_id_map:
                        seat_type = self.seat_id_map[seat_id]['type']
                        self.ticket_type_tab.update_type_row(seat_type)

    def process_single_seat_update(self, seat_data):
        """
        Ultra-optimized seat update processing with critical path optimizations.
        Reduces auto-hold decision time to absolute minimum with early exits.
        """
        # Quick check for required data (FAST PATH)
        if not seat_data or 'objectLabelOrUuid' not in seat_data:
            return

        # Extract seat_id for use in all checks
        seat_id = seat_data['objectLabelOrUuid']

        # ===== FAST PATH - EARLY EXITS =====
        
        # 1. Skip if status isn't free (first and fastest check)
        status = seat_data.get('status', '')
        if status != 'free':
            return
        
        # 2. Skip if already being processed (nearly as fast)
        if seat_id in self.auto_held_seats:
            return
        
        # 3. Skip if auto-hold is disabled (very fast)
        if not self.ticket_type_tab.auto_hold_checkbox.isChecked():
            return
        
        # 4. Skip if being transferred (check this early)
        if seat_id in self.token_system.transfer_in_progress_seats:
            return

        # 5. Fast check for max tickets
        # Get these values just once to avoid repeated property access
        max_tickets = 0
        try:
            max_tickets_text = self.total_tickets_edit.text()
            if max_tickets_text:
                max_tickets = int(max_tickets_text)
        except ValueError:
            pass

        if max_tickets > 0:
            # Fast seat count without locks
            current_held_count = len(self.token_system.get_all_seats())
            pending_holds = getattr(self, 'pending_seats', 0)
            
            if current_held_count + pending_holds >= max_tickets:
                return
        
        # ===== TICKET TYPE DETERMINATION (Optimized) =====
        
        # Fast type extraction - avoid string operations when possible
        seat_parts = seat_id.split('-', 1)  # Split only at first dash
        seat_type = seat_parts[0].strip() if seat_parts else seat_id
        
        # Fast path for "hold all" mode
        hold_all = getattr(self.ticket_type_tab, 'hold_all_enabled', False)
        
        # Only check selected types if not in "hold all" mode
        if not hold_all:
            selected_types = self.ticket_type_tab.get_selected_types()
            if '*' not in selected_types and seat_type not in selected_types:
                return
        
        # ===== EXECUTION PHASE (Critical path) =====
        
        # At this point, we've decided to hold the seat
        # Use an atomic update to mark the seat as pending
        with self.data_lock:
            if seat_id in self.auto_held_seats:
                return

            # Mark as pending with websocket timestamp for accurate performance tracking
            websocket_timestamp = seat_data.get('_websocket_timestamp')
            if websocket_timestamp:
                # Use websocket message timestamp for accurate performance measurement
                self.auto_held_seats[seat_id] = websocket_timestamp
                logger.debug(f"Using websocket timestamp {websocket_timestamp} for seat {seat_id}")
            else:
                # Fallback to current time if websocket timestamp not available
                self.auto_held_seats[seat_id] = time.time()
                logger.debug(f"No websocket timestamp available for seat {seat_id}, using current time")

            self.pending_seats += 1

        # Launch the actual hold without holding locks
        self._launch_async_hold(seat_id, seat_type)


    def _launch_async_hold(self, seat_id, seat_type):
        """Launch seat holding using SimpleHoldManager"""
        if hasattr(self, 'simple_hold_manager') and self.simple_hold_manager:
            # Run hold operation in background thread
            def hold_async():
                try:
                    success = self.simple_hold_manager.hold_seat_async(seat_id)
                    if not success:
                        # If SimpleHoldManager failed, try fallback async method
                        QTimer.singleShot(0, lambda: self._try_fallback_hold(seat_id))
                except Exception as e:
                    QTimer.singleShot(0, lambda: self.log(f"❌ Error in SimpleHoldManager for seat {seat_id}: {str(e)}"))

            # Run in background thread
            threading.Thread(target=hold_async, daemon=True).start()
            return True
        else:
            # Fallback to old async method if SimpleHoldManager not available
            return self._try_fallback_hold(seat_id)

    def _try_fallback_hold(self, seat_id):
        """Fallback async hold method"""
        def hold_async():
            try:
                result = self.run_async(self._hold_seat_async(seat_id))
                if result:
                    # Schedule UI update on main thread
                    QTimer.singleShot(0, lambda: self.on_async_seat_held(seat_id, result))
                else:
                    QTimer.singleShot(0, lambda: self.log(f"❌ Failed to hold seat {seat_id}"))
            except Exception as e:
                QTimer.singleShot(0, lambda: self.log(f"❌ Error holding seat {seat_id}: {str(e)}"))

        # Run in background thread
        threading.Thread(target=hold_async, daemon=True).start()
        return True

    async def _hold_seat_async(self, seat_id: str):
        """Async method to hold a seat"""
        try:
            if not hasattr(self, 'event_key') or not self.event_key:
                logger.error("No event key available for holding seat")
                return None

            # Get a hold token from the token system
            if not self.token_system:
                logger.error("No token system available")
                return None

            # Get available tokens
            tokens = self.token_system.get_available_tokens()
            if not tokens:
                logger.error("No available tokens for holding seat")
                return None

            hold_token = tokens[0]  # Use first available token

            # Hold the seat using async helper
            success = await hold_seat(
                seat_number=seat_id,
                event_key=self.event_key,
                hold_token=hold_token,
                channel_keys=getattr(self, 'channel_keys', []),
                proxy=self.get_proxy_string()
            )

            if success:
                return hold_token
            else:
                return None

        except Exception as e:
            logger.error(f"Error in async seat hold: {str(e)}")
            return None

    async def _release_seat_async(self, seat_id: str, hold_token: str):
        """Async method to release a seat"""
        try:
            if not hasattr(self, 'event_key') or not self.event_key:
                logger.error("No event key available for releasing seat")
                return False

            # Release the seat using async helper
            success = await release_seat(
                seat_number=seat_id,
                event_key=self.event_key,
                hold_token=hold_token,
                proxy=self.get_proxy_string()
            )

            return success

        except Exception as e:
            logger.error(f"Error in async seat release: {str(e)}")
            return False

    def release_seat_sync(self, seat_id: str, hold_token: str):
        """Synchronous wrapper for releasing seats"""
        def release_async():
            try:
                result = self.run_async(self._release_seat_async(seat_id, hold_token))
                if result:
                    QTimer.singleShot(0, lambda: self.log(f"✅ Released seat {seat_id}"))
                else:
                    QTimer.singleShot(0, lambda: self.log(f"❌ Failed to release seat {seat_id}"))
            except Exception as e:
                QTimer.singleShot(0, lambda: self.log(f"❌ Error releasing seat {seat_id}: {str(e)}"))

        # Run in background thread
        threading.Thread(target=release_async, daemon=True).start()


    def _process_non_free_seat(self, seat_data):
        """Process non-free seats with lower priority"""
        # Schedule this to run after critical operations
        QTimer.singleShot(50, lambda: self._update_seat_data(seat_data))

    def _process_non_auto_seat(self, seat_data):
        """Process free seats that we're not auto-holding"""
        # Schedule this to run after critical operations
        QTimer.singleShot(10, lambda: self._update_seat_data(seat_data))

    def _update_seat_data(self, seat_data):
        """Update internal seat data structures (non-critical path)"""
        try:
            with self.data_lock:
                seat_id = seat_data['objectLabelOrUuid']
                new_status = seat_data.get('status', 'free').lower()

                # Extract seat type
                seat_parts = seat_id.split('-')
                seat_type = seat_parts[0].strip() if seat_parts else seat_id

                # Get existing seat info
                current_info = self.seat_id_map.get(seat_id)

                if current_info:
                    # Seat already tracked - update status
                    previous_status = current_info['status']
                    if new_status != previous_status:
                        # Remove from old status dictionary
                        if seat_type in self.tickets_info and previous_status in self.tickets_info[seat_type]:
                            self.tickets_info[seat_type][previous_status].pop(seat_id, None)

                        # Add to new status dictionary
                        if seat_type not in self.tickets_info:
                            self.tickets_info[seat_type] = {}
                        if new_status not in self.tickets_info[seat_type]:
                            self.tickets_info[seat_type][new_status] = {}
                        self.tickets_info[seat_type][new_status][seat_id] = seat_data
                        self.seat_id_map[seat_id]['status'] = new_status
                else:
                    # New seat - add to tracking
                    self.seat_id_map[seat_id] = {
                        'type': seat_type,
                        'status': new_status
                    }

                    # Add to status dictionary
                    if seat_type not in self.tickets_info:
                        self.tickets_info[seat_type] = {}
                    if new_status not in self.tickets_info[seat_type]:
                        self.tickets_info[seat_type][new_status] = {}
                    self.tickets_info[seat_type][new_status][seat_id] = seat_data

            # Update UI for this seat type
            self.ticket_type_tab.update_type_row(seat_type)
        except Exception as e:
            logger.error(f"Error updating seat data: {str(e)}")

    #------------------------------------------------------------------
    # Token refresh and management
    #------------------------------------------------------------------

    def refresh_all_token_times(self):
        """
        Refresh the time left for all tokens by calling activate_hold_token
        Now only uses the token system
        """
        logger.debug("refresh_all_token_times called")

        # Use token system, which handles its own refresh
        if self.token_system:
            self.held_seats_tab.auto_refresh_held_seats(True)
            return

        # No token system or tokens - just log a message
        # self.log("No token system initialized. Cannot refresh token times.")



    def on_token_refresh_finished(self, count):
        """Called when all token refreshing is finished"""
        if count > 0:
            self.log(f"Refreshed {count} token times from server")

        # Update the UI times - but only the time columns, not full rebuild
        if hasattr(self, 'held_seats_tab'):
            self.held_seats_tab.update_time_displays_only()

    #------------------------------------------------------------------
    # UI Setup and Event Handlers
    #------------------------------------------------------------------

    def create_booking_tab_ui(self):
        """Create the main booking tab UI"""
        layout = QVBoxLayout()

        self.auth_name_edit = QLineEdit()
        self.auth_name_edit.setPlaceholderText("Enter your username...")

        self.hwid_check_button = QPushButton("Check username")

        self.event_key_edit = QLineEdit()
        self.event_key_edit.setPlaceholderText("Enter event key...")

        self.total_tickets_edit = QLineEdit()
        self.total_tickets_edit.setPlaceholderText("Enter total seats to book...")

        self.team_combo = QComboBox()

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)

        self.load_button = QPushButton("Load Event Info")
        self.book_button = QPushButton("Book Seats (Concurrent)")
        self.release_button = QPushButton("Release All Seats")
        self.about_button = QPushButton("About")
        self.configure_proxy_button = QPushButton("Configure Proxy")
        self.performance_button = QPushButton("Performance Monitor")

        self.performance_button.clicked.connect(self.show_performance_monitor)

        form_layout = QFormLayout()
        form_layout.addRow("Auth Name:", self.auth_name_edit)
        form_layout.addRow("", self.hwid_check_button)
        form_layout.addRow("Event Key:", self.event_key_edit)
        form_layout.addRow("Total Seats:", self.total_tickets_edit)
        form_layout.addRow("Choose Team:", self.team_combo)
        form_layout.addRow(self.configure_proxy_button)
        form_layout.addRow(self.performance_button)

        button_layout = QHBoxLayout()
        button_layout.addWidget(self.load_button)
        button_layout.addWidget(self.book_button)
        button_layout.addWidget(self.release_button)
        button_layout.addWidget(self.about_button)

        layout.addLayout(form_layout)
        layout.addLayout(button_layout)
        layout.addWidget(self.log_text)

        self.booking_tab.setLayout(layout)

        # Disable until HWID check
        self.set_fields_enabled(False)
        self.about_button.setEnabled(True)

        # Connect signals
        self.configure_proxy_button.clicked.connect(self.on_configure_proxy)
        self.hwid_check_button.clicked.connect(self.on_hwid_check)
        self.load_button.clicked.connect(self.on_load_event_info)
        self.book_button.clicked.connect(self.on_book_seats)
        self.release_button.clicked.connect(self.on_release_seats)
        self.about_button.clicked.connect(self.on_about_clicked)

    def set_fields_enabled(self, enabled: bool):
        """Enable or disable form fields"""
        self.event_key_edit.setEnabled(enabled)
        self.total_tickets_edit.setEnabled(enabled)
        self.team_combo.setEnabled(enabled)
        self.load_button.setEnabled(enabled)
        self.book_button.setEnabled(enabled)
        self.release_button.setEnabled(enabled)

    def on_configure_proxy(self):
        """Handle proxy configuration button click with enhanced proxy system"""
        dialog = ProxyConfigDialog(self)

        # Set current proxy configuration
        current_config = {
            "enabled": self.proxy_config.get("enabled", False),
            "domain": self.proxy_config.get("domain", "p.webshare.io"),
            "port": self.proxy_config.get("port", "80"),
            "username": self.proxy_config.get("username", "taplmftg-rotate"),
            "password": self.proxy_config.get("password", ""),
            "proxy_list": self.proxy_config.get("proxy_list", []),
            "use_rotating": self.proxy_config.get("use_rotating", True),
            "api_token": self.proxy_config.get("api_token", ""),
            "rotation_count": self.proxy_config.get("rotation_count", 50),
            "local_rotation": self.proxy_config.get("local_rotation", True),
            "mode": self.proxy_config.get("mode", "single")
        }

        if dialog.exec_() == dialog.Accepted:
            new_settings = dialog.get_proxy_settings()
            self.proxy_config = new_settings

            # Initialize the global proxy manager with new settings
            proxy_manager = get_global_proxy_manager(new_settings)
            self.initialize_proxy_system()
            # Log proxy configuration
            if new_settings["mode"] == "single":
                if new_settings["enabled"]:
                    if new_settings["use_rotating"]:
                        self.log(f"Proxy enabled: {new_settings['domain']}:{new_settings['port']} with rotation (User: {new_settings['username']})")
                    else:
                        self.log(f"Proxy enabled: {new_settings['domain']}:{new_settings['port']} without rotation (User: {new_settings['username']})")
                else:
                    self.log("Proxy disabled.")
            else:
                proxy_count = len(new_settings.get("proxy_list", []))
                if new_settings["local_rotation"]:
                    self.log(f"Local proxy rotation enabled with {proxy_count} proxies. Rotating every {new_settings['rotation_count']} requests.")
                else:
                    self.log(f"Local proxy list enabled with {proxy_count} proxies. No automatic rotation.")
        else:
            self.log("Proxy config canceled by user.")


    def record_proxy_performance(self, proxy_str, success, response_time_ms):
        """Record proxy performance metrics"""
        proxy_manager = get_global_proxy_manager()
        if proxy_manager and proxy_str:
            proxy_manager.report_result(proxy_str, success, response_time_ms)

    def on_hwid_check(self):
        """Handle hardware ID check button click"""
        username = self.auth_name_edit.text().strip()
        if not username:
            QMessageBox.warning(self, "Warning", "Please enter a valid username.")
            return

        if perform_hwid_check(username):
            QMessageBox.information(self, "Success", f"Welcome {username}!")
            self.set_fields_enabled(True)
            self.held_seats_tab.setEnabled(True)
            self.ticket_type_tab.setEnabled(True)
            self.log(f"Welcome, {username}!")
            self.setWindowTitle(f"{SCRIPT_NAME} v{VERSION} - {username}")
            logger.info(f"HWID check passed for user {username}.")
        else:
            QMessageBox.critical(self, "Error", "Username check failed.")
            logger.error(f"HWID check failed for user {username}.")
            self.set_fields_enabled(False)
            self.held_seats_tab.setEnabled(False)
            self.ticket_type_tab.setEnabled(False)

    def reload_event_data_and_reconnect(self):
        """Reload event data and reconnect WebSocket when connection is lost"""
        # Only reload if we have event data already
        if not hasattr(self, 'webook_data') or not self.webook_data:
            self.log("[WebSocket] No event data to reload")
            return False

        event_key = self.webook_data['data']['seats_io']['event_key']
        self.log(f"[WebSocket] Reloading event data for {event_key} after disconnection...")

        try:
            # Reload webook data using WebookClient
            client = WebookClient(proxy=self.get_proxy_string())
            try:
                self.webook_data = client.get_event_info(event_key=event_key)
                if not self.webook_data:
                    self.log(f'[WebSocket] Failed to reload event data')
                    return False

                data = self.webook_data["data"]["seats_io"]
                data['event_info'] = self.run_async(get_event_seatsio_info(data, proxy=self.get_proxy_string()))
            finally:
                client.close()

            # Reload seats data
            chart_key = data["chart_key"]
            seats = get_object_statuses(data["event_key"], chart_key, proxy=self.get_proxy_string())
            self.tickets_info = group_tickets_by_type_and_status(seats, free_only=False)

            # Reinitialize seat map
            for ttype, statuses in self.tickets_info.items():
                for status, seats in statuses.items():
                    for seat in seats:
                        seat_id = seat
                        self.seat_id_map[seat_id] = {
                            'type': ttype,
                            'status': status
                        }

            # Reinitialize WebSocket with fresh connection
            self._initialize_websocket(data)

            # Refresh UI
            self._update_initial_ui()

            self.log("[WebSocket] Event data reloaded and WebSocket reconnected successfully")
            return True

        except Exception as e:
            self.log(f"[WebSocket] Error reloading event data: {str(e)}")
            logger.exception("Error reloading event data", exc_info=True)
            return False

    # Modify the on_websocket_connected method in main_window.py
    def on_websocket_connected(self, connected):
        """Handle WebSocket connection status changes"""
        if connected:
            self.log("[WebSocket] Connected to event updates.")
        else:
            self.log("[WebSocket] Disconnected from event updates. Will attempt to reconnect...")
            # Schedule a reload attempt after a short delay
            QTimer.singleShot(5000, self.reload_event_data_and_reconnect)

    def show_performance_monitor(self):
        """Show the performance monitor dialog"""
        if not hasattr(self, 'performance_stats'):
            # Create stats tracker if it doesn't exist
            self.performance_stats = PerformanceStats()

            # Performance stats will be updated by the async system

        # Create and show the dialog
        dialog = PerformanceMonitorDialog(self.performance_stats, self)
        dialog.exec_()

    def on_about_clicked(self):
        """Handle about button click"""
        msg = QMessageBox(self)
        msg.setWindowTitle("About Webook Booking Pro")
        msg.setTextFormat(Qt.RichText)
        msg.setText(
            f"<h3>Webook Booking Pro version {VERSION}</h3>"
            "<p>Visit our website: "
            "<a href='https://elitesoftworks.com'>elitesoftworks.com</a></p>"
            "<p>Join our Telegram: "
            "<a href='https://t.me/NoodlesRush'>@NoodlesRush</a></p>"
        )
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec_()

    def log(self, message: str):
        """Writes to the GUI log and system logger"""
        self.log_text.append(message)
        logger.info(message)

    def on_load_event_info(self):
        """Handle load event info button click with async logic"""
        event_key = self.event_key_edit.text().strip()
        if not event_key:
            QMessageBox.warning(self, "Warning", "Please enter an event key.")
            return

        self.log(f"🔄 Loading event info for: {event_key}")

        # Extract event_key from URL if needed
        is_season = False
        if '/' in event_key:
            found = re.search(r'events?/(.*?)(?:/|$)', event_key)
            if found:
                event_key = found.group(1)
            else:
                found = re.search(r'seasons?/(.*?)(?:/|$)', event_key)
                if found:
                    event_key = found.group(1)
                    is_season = True

        # Run the async loading in a separate thread to avoid blocking UI
        def load_async():
            try:
                # Run async operations
                result = self.run_async(self._load_event_info_async(event_key, is_season))
                if result:
                    # Schedule UI update on main thread
                    QTimer.singleShot(0, lambda: self._finalize_event_loading(result))
                else:
                    QTimer.singleShot(0, lambda: self.log("❌ Failed to load event info"))
            except Exception as e:
                error_msg = f"Failed to load event info: {str(e)}"
                QTimer.singleShot(0, lambda: self._handle_loading_error(error_msg, e))

        # Run in background thread
        threading.Thread(target=load_async, daemon=True).start()

    async def _load_event_info_async(self, event_key: str, is_season: bool):
        """Async method to load event information"""
        try:
            # Get event data using WebookClient (synchronous call in async context)
            def get_event_data():
                client = WebookClient(proxy=self.get_proxy_string())
                try:
                    return client.get_event_info(event_key=event_key, is_season=is_season)
                finally:
                    client.close()

            # Run the synchronous WebookClient call in a thread pool
            event_data = await asyncio.get_event_loop().run_in_executor(None, get_event_data)
            if not event_data:
                return None

            # Cache the event ID
            cache_event_id(event_data)

            # Get seats data
            data = event_data["data"]["seats_io"]
            if is_season:
                data['event_key'] = data['season_key']

            # Get additional data concurrently
            event_info_task = get_event_seatsio_info(data, proxy=self.get_proxy_string())
            seats_task = get_object_statuses(data["event_key"], data["chart_key"], proxy=self.get_proxy_string())

            # Wait for both tasks to complete
            data['event_info'], seats = await asyncio.gather(event_info_task, seats_task)

            return {
                'webook_data': event_data,
                'seats': seats,
                'data': data
            }

        except Exception as e:
            logger.error(f"Error in async event loading: {str(e)}")
            return None

    def _finalize_event_loading(self, result):
        """Finalize event loading on the main thread"""
        try:
            self.webook_data = result['webook_data']
            seats = result['seats']
            data = result['data']

            self.tickets_info = group_tickets_by_type_and_status(seats, free_only=False)

            # Populate team combo
            self.team_combo.clear()
            if "home_team" in self.webook_data["data"] and self.webook_data["data"]["home_team"]:
                home_team = self.webook_data["data"]["home_team"]
                self.team_combo.addItem(home_team["name"], home_team["_id"])
            if "away_team" in self.webook_data["data"] and self.webook_data["data"]["away_team"]:
                away_team = self.webook_data["data"]["away_team"]
                self.team_combo.addItem(away_team["name"], away_team["_id"])

            # Build seat map
            self.seat_id_map.clear()
            for ttype, statuses in self.tickets_info.items():
                for status, seats in statuses.items():
                    for seat in seats:
                        seat_id = seat
                        self.seat_id_map[seat_id] = {
                            'type': ttype,
                            'status': status
                        }

            # Initialize systems
            self._initialize_websocket(data)
            self._update_initial_ui()
            self._initialize_token_management_system()
            self._start_simple_holding_system()

            # Log success
            event_id = get_cached_event_id()
            if event_id:
                self.log(f"✅ Event loaded successfully. Event ID: {event_id}")
            else:
                self.log("✅ Event loaded successfully (no event ID cached)")

        except Exception as e:
            self._handle_loading_error(f"Failed to finalize event loading: {str(e)}", e)

    def _handle_loading_error(self, error_msg: str, exception: Exception):
        """Handle event loading errors"""
        self.log(f"❌ {error_msg}")
        logger.exception("Failed to load event info", exc_info=True)
        QMessageBox.critical(self, "Error", error_msg)

    def on_websocket_connected(self, connected):
        """Handle WebSocket connection status changes"""
        if connected:
            self.log("[WebSocket] Connected to event updates.")
        else:
            self.log("[WebSocket] Disconnected from event updates.")

    def on_token_renewed(self, token_id, time_left):
        """Handle token renewed signal"""
        self.log(f"Token {token_id} renewed with {time_left} seconds remaining")
        # Force UI refresh with rebuild
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

    # Performance tracking removed with FastHoldManager

    def on_refresh_error(self, error_message):
        """Handle refresh errors from WebSocket or workers"""
        self.log(f"[WebSocket/Worker] Error: {error_message}")
        logger.error(f"[WebSocket/Worker] Error: {error_message}")

    def on_book_seats(self):
        """Handle book seats button click with simplified booking"""
        if not self.token_system:
            self.log("❌ Token system not initialized. Please load an event first.")
            return

        # Get total seats to book
        try:
            total_seats = int(self.total_tickets_edit.text())
            if total_seats <= 0:
                raise ValueError("Must be positive")
        except ValueError:
            QMessageBox.warning(self, "Warning", "Please enter a valid number of seats to book.")
            return

        # Get team ID
        team_id = self.team_combo.currentData()
        if not team_id:
            QMessageBox.warning(self, "Warning", "Please select a team.")
            return

        # Simple booking implementation
        self.log(f"🚀 Starting booking of {total_seats} seats...")

        # For now, just create tokens - the actual seat booking will be handled by auto-hold
        try:
            created_tokens = 0
            for i in range(total_seats):
                success = self.token_system.create_token()
                if success:
                    created_tokens += 1
                    self.log(f"✅ Created token {i+1}/{total_seats}")
                else:
                    self.log(f"❌ Failed to create token {i+1}/{total_seats}")

            self.log(f"✅ Booking setup complete. Created {created_tokens}/{total_seats} tokens.")
            self.log("ℹ️ Enable auto-hold in the Ticket Type Selection tab to automatically hold seats.")

        except Exception as e:
            self.log(f"❌ Error during booking: {str(e)}")

    def cancel_booking(self):
        """Cancel the current booking operation - simplified version"""
        self.log("ℹ️ Booking cancelled by user")

    def cancel_booking(self):
        """Cancel the booking process"""
        if hasattr(self, 'booking_worker') and self.booking_worker.isRunning():
            self.log("🛑 Cancelling booking process...")
            self.booking_worker.stop()
            # Restore book button
            if hasattr(self, 'book_button_text'):
                self.book_button.setText(self.book_button_text)
                self.book_button.clicked.disconnect()
                self.book_button.clicked.connect(self.on_book_seats)

    def get_proxy_string(self):
        """Get a proxy string using the proxy manager for optimal selection"""
        # Use proxy manager to get the best proxy
        proxy_manager = get_global_proxy_manager()

        # If proxy is disabled or no proxies available, return None
        if not self.proxy_config.get("enabled", False):
            return None

        # Get the proxy based on the mode
        if self.proxy_config.get("mode", "single") == "single":
            # Single proxy mode - Format manually
            domain = self.proxy_config["domain"]
            port = self.proxy_config["port"]
            user = self.proxy_config["username"]
            pwd = self.proxy_config["password"]

            # Return in format appropriate for the system
            return f"{domain}:{port}:{user}:{pwd}"
        else:
            # List mode - Get best proxy from manager
            proxy_str = proxy_manager.get_proxy()
            return proxy_str


    def reload_event_data(self, event_key=None, force_reload=False):
        """
        Reload event data and update the application state.
        
        Args:
            event_key: Event key to reload, or None to use current event key
            force_reload: Whether to force a reload even if channel keys haven't changed
        
        Returns:
            bool: True if reload was successful, False otherwise
        """
        # Use current event key if none provided
        if not event_key and hasattr(self, 'webook_data') and self.webook_data:
            event_key = self.webook_data['data']['seats_io']['event_key']
        
        if not event_key:
            self.log("❌ No event key available for reload")
            return False
        
        self.log(f"🔄 Reloading event data for {event_key}")
        
        try:
            # Get latest event info using WebookClient
            client = WebookClient(proxy=self.get_proxy_string())
            try:
                new_data = client.get_event_info(event_key=event_key)

                if not new_data:
                    self.log(f'❌ Failed to reload event data')
                    return False
            finally:
                client.close()
            
            # Check if channel keys have changed (if not forcing reload)
            if not force_reload and hasattr(self, 'webook_data') and self.webook_data:
                old_keys = self.webook_data['data']['channel_keys']
                new_keys = new_data['data']['channel_keys']
                
                old_keys_json = json.dumps(old_keys, sort_keys=True)
                new_keys_json = json.dumps(new_keys, sort_keys=True)
                
                if old_keys_json == new_keys_json:
                    self.log("✓ Channel keys are unchanged, skipping reload")
                    return True
                else:
                    self.log("🔄 Channel keys have changed, updating data")
            
            # Update webook data
            self.webook_data = new_data
            
            # Process data
            data = self.webook_data["data"]["seats_io"]
            data['event_info'] = get_event_seatsio_info(data, proxy=self.get_proxy_string())
            
            # Cache the event ID for token creation
            cache_event_id(self.webook_data)
            
            # Reload seats data
            chart_key = data["chart_key"]
            seats = get_object_statuses(data["event_key"], chart_key, proxy=self.get_proxy_string())
            self.tickets_info = group_tickets_by_type_and_status(seats, free_only=False)
            
            # Rebuild seat map
            self.seat_id_map.clear()
            for ttype, statuses in self.tickets_info.items():
                for status, seats in statuses.items():
                    for seat in seats:
                        seat_id = seat
                        self.seat_id_map[seat_id] = {
                            'type': ttype,
                            'status': status
                        }
            
            # Update subsystems with new channel keys
            if hasattr(self, 'token_system') and self.token_system:
                self.token_system.channel_keys = self.webook_data['data']['channel_keys']
                
            # Channel keys are now stored directly in the main window
            self.channel_keys = self.webook_data['data']['channel_keys']
            
            # Reload WebSocket if needed
            if hasattr(self, 'websocket_manager') and self.websocket_manager:
                # Stop current websocket
                self.websocket_manager.stop()
                # Initialize with fresh data
                self._initialize_websocket(data)
            
            # Update UI
            self._update_initial_ui()
            
            self.log("✅ Event data reloaded successfully")
            return True
            
        except Exception as e:
            self.log(f"❌ Error reloading event data: {str(e)}")
            logger.error(f"Error reloading event data: {str(e)}", exc_info=True)
            return False
        
    def on_booking_finished(self, result):
        """Handle completion of booking worker with booking statistics"""
        # Clean up the worker thread
        sender_worker = self.sender()
        if isinstance(sender_worker, QThread):
            sender_worker.quit()
            sender_worker.wait()
            sender_worker.deleteLater()

        if result and result.get("success", False):
            booked_count = result.get("booked_count", 0)
            token_count = result.get("token_count", 0)
            booked_seats = result.get("booked_seats", {})

            self.log(f"✅ Booked {booked_count} seats with {token_count} tokens")

            # Register all booked seats with the token system
            if self.token_system:
                seat_count = 0
                for token_id, seat_ids in booked_seats.items():
                    for seat_id in seat_ids:
                        self.token_system.register_held_seat(seat_id, token_id)
                        seat_count += 1

                self.log(f"📦 Added {seat_count} seats to token management system")
            else:
                self.log(f"⚠️ Token system not initialized, seats won't be managed automatically")
        else:
            error = result.get("error", "Unknown error") if result else "Unknown error"
            self.log(f"❌ Booking failed: {error}")

        # Restore book button
        if hasattr(self, 'book_button_text'):
            self.book_button.setText(self.book_button_text)
            self.book_button.clicked.disconnect()
            self.book_button.clicked.connect(self.on_book_seats)

        # Refresh seats UI
        self.held_seats_tab.auto_refresh_held_seats()


    def on_release_seats(self):
        """Handle release seats button click"""
        if self.token_system:
            # Get all seats
            all_seats = self.token_system.get_all_seats()
            if not all_seats:
                self.log("[Main] No seats to release.")
                return

            # Confirm with user
            msg = QMessageBox.question(
                self,
                "Confirm Release",
                f"Are you sure you want to release all {len(all_seats)} seats?",
                QMessageBox.Yes | QMessageBox.No
            )

            if msg != QMessageBox.Yes:
                return

            # Release all seats
            released_count = 0
            for seat_id in list(all_seats.keys()):
                if self.token_system.release_seat(seat_id):
                    released_count += 1

            self.log(f"Released {released_count}/{len(all_seats)} seats")
            self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
            return

        # No token system initialized
        self.log("[Main] Token system not initialized. Cannot release seats.")


    def get_now(self) -> float:
        """Helper so we can easily mock time if needed."""
        return time.time()

    def run_async(self, coro):
        """Run an async coroutine in a new event loop"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()

    def closeEvent(self, event: QEvent):
        """Clean up resources when window is closed"""
        # Stop threads
        if self.websocket_manager:
            self.websocket_manager.stop()
            self.websocket_manager = None

        if hasattr(self, 'time_left_updater') and self.time_left_updater and self.time_left_updater.isRunning():
            self.time_left_updater.stop()
            self.time_left_updater.wait()


        if hasattr(self, 'token_system') and self.token_system:
            self.token_system.shutdown()

        # Clean up simple hold manager
        if hasattr(self, 'simple_hold_manager') and self.simple_hold_manager:
            self.simple_hold_manager.cleanup()

        # Cleanly shut down token renewal timer
        if hasattr(self, 'renewal_timer') and self.renewal_timer.isActive():
            self.renewal_timer.stop()

        # Stop channel key refresh timer
        if hasattr(self, 'channel_key_timer') and self.channel_key_timer.isActive():
            self.channel_key_timer.stop()

        # Let parent handle the rest
        super().closeEvent(event)
